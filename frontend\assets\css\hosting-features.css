/*
 * Hosting Features CSS
 * Styles for hosting features sections with light/dark mode support
 */

/* Import company brand colors and variables */
@import url('variables.css');

/* Light mode variables (default) */
:root {
    --hosting-bg: var(--gray-50);
    --section-title-color: var(--gray-800);
    --features-title-color: var(--gray-800);
    --features-subtitle-color: var(--gray-600);
    --features-text-color: var(--gray-600);
    --feature-content-color: var(--gray-800);
    --feature-description-color: var(--gray-600);
    --hosting-item-bg: var(--white);
    --hosting-item-shadow: var(--shadow-sm);
    --hosting-item-hover-shadow: var(--shadow-md);
    --feature-box-bg: var(--white);
    --feature-box-shadow: var(--shadow-sm);
    --feature-box-hover-shadow: var(--shadow-lg);
    --feature-icon-color: var(--primary-color);
}

/* Dark mode variables */
[data-bs-theme="dark"] {
    --hosting-bg: var(--gray-900);
    --section-title-color: var(--gray-50);
    --features-title-color: var(--gray-50);
    --features-subtitle-color: var(--gray-400);
    --features-text-color: var(--gray-400);
    --feature-content-color: var(--gray-50);
    --feature-description-color: var(--gray-400);
    --hosting-item-bg: var(--gray-800);
    --hosting-item-shadow: var(--shadow-md);
    --hosting-item-hover-shadow: var(--shadow-lg);
    --feature-box-bg: var(--gray-800);
    --feature-box-shadow: var(--shadow-md);
    --feature-box-hover-shadow: var(--shadow-xl);
    --feature-icon-color: var(--primary-color);
}

/* Hosting Features Section */
.hosting-features {
    padding: 4rem 0;
}

.section-title {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    line-height: 1.3;
    color: var(--section-title-color);
}

.hosting-image-wrapper {
    padding: 1rem;
    max-width: 90%;
    margin: 0 auto;
}

.hosting-image {
    width: 100%;
    height: auto;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

[data-bs-theme="dark"] .hosting-image {
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.features-content {
    padding: 1rem;
}

.features-title {
    font-size: 2rem;
    margin-bottom: 1rem;
    line-height: 1.3;
    color: var(--features-title-color);
}

.features-subtitle {
    font-size: 1.25rem;
    color: var(--features-subtitle-color);
    margin-bottom: 1.5rem;
    line-height: 1.5;
}

.features-text {
    color: var(--features-text-color);
    margin-bottom: 2rem;
    line-height: 1.6;
    font-size: 1.1rem;
}

.features-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.hosting-item {
    background: var(--hosting-item-bg);
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: var(--hosting-item-shadow);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    align-items: flex-start;
}

.hosting-item:hover {
    transform: translateY(-3px);
    box-shadow: var(--hosting-item-hover-shadow);
}

/* Override the hover styles from widdx-style.css */
.hosting-item:hover {
    background-color: var(--hosting-item-bg) !important;
    color: var(--feature-content-color) !important;
}

.hosting-item:hover .feature-icon {
    color: var(--feature-icon-color) !important;
}

.feature-icon {
    color: var(--feature-icon-color);
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1.5rem;
    flex-shrink: 0;
}

.feature-content {
    flex: 1;
}

.feature-content strong {
    display: block;
    margin-bottom: 0.75rem;
    font-size: 1.2rem;
    color: var(--feature-content-color);
}

.feature-content p {
    margin: 0;
    line-height: 1.6;
    color: var(--feature-description-color);
    font-size: 1rem;
}

/* Feature Boxes Section */
.features-boxes {
    padding: 4rem 0;
}

.feature-box {
    background: var(--feature-box-bg);
    padding: 2.5rem 2rem;
    border-radius: 12px;
    box-shadow: var(--feature-box-shadow);
    transition: all 0.3s ease;
    height: 100%;
}

.feature-box:hover {
    transform: translateY(-5px);
    box-shadow: var(--feature-box-hover-shadow);
}

/* Override the hover styles from widdx-style.css */
.feature-box:hover {
    background-color: var(--feature-box-bg) !important;
}

.feature-box:hover .icon-box i,
.feature-box:hover h5,
.feature-box:hover p {
    color: inherit !important;
}

/* Icon box styling */
.icon-box {
    color: var(--feature-icon-color);
    transition: color 0.3s ease;
}

[data-bs-theme="dark"] .icon-box {
    color: var(--feature-icon-color);
}

.feature-box:hover .icon-box {
    color: var(--feature-icon-color) !important;
}

/* Ensure text-primary is properly styled in dark mode */
[data-bs-theme="dark"] .text-primary {
    color: var(--feature-icon-color) !important;
}

.feature-title {
    color: var(--feature-content-color);
    font-size: 1.4rem;
    margin-bottom: 1rem;
    line-height: 1.4;
}

.feature-description {
    color: var(--feature-description-color);
    line-height: 1.6;
    font-size: 1rem;
    margin: 0;
}

/* Tablet Styles */
@media (max-width: 991px) {
    .hosting-features,
    .features-boxes {
        padding: 3rem 0;
    }

    .section-title {
        font-size: 2.2rem;
    }

    .features-title {
        font-size: 1.8rem;
        margin-top: 1rem;
    }

    .hosting-image-wrapper {
        max-width: 100%;
        padding: 0 1rem;
    }

    .feature-box {
        padding: 2rem;
    }
}

/* Mobile Styles */
@media (max-width: 767px) {
    .hosting-features,
    .features-boxes {
        padding: 2.5rem 0;
    }

    .container {
        padding-left: 1.25rem;
        padding-right: 1.25rem;
    }

    .section-title {
        font-size: 1.8rem;
        margin-bottom: 1.5rem;
    }

    .features-title {
        font-size: 1.6rem;
    }

    .features-subtitle {
        font-size: 1.1rem;
    }

    .features-text {
        font-size: 1rem;
    }

    .hosting-item {
        padding: 1.25rem;
    }

    .feature-icon {
        width: 40px;
        height: 40px;
        margin-right: 1rem;
    }

    .feature-content strong {
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
    }

    .feature-box {
        padding: 1.75rem 1.5rem;
    }

    .feature-title {
        font-size: 1.3rem;
    }
}

/* Extra Small Screens */
@media (max-width: 575px) {
    .hosting-features,
    .features-boxes {
        padding: 2rem 0;
    }

    .section-title {
        font-size: 1.6rem;
    }

    .features-list {
        gap: 1rem;
    }

    .hosting-item {
        padding: 1.25rem;
    }

    .feature-box {
        padding: 1.5rem;
    }

    .feature-icon {
        font-size: 0.9em;
    }
}
