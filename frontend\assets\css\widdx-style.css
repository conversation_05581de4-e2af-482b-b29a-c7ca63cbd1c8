/* Import company brand colors and variables */
@import url('variables.css');

/* CSS Index
1. Search Modal
2. Header Design
3. Sidebar Design
4. Sidebar Header
5. Sidebar Content
6. Search Box Design
7. Navigation List Enhancement
8. Social Icons Styles
*/
#body {
  font-family: "Cairo", sans-serif;
}
/* Search Modal */
/* Search Modal */
#search-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

/* Dark mode for search modal */
[data-bs-theme="dark"] #search-modal {
  background-color: rgba(var(--gray-900), 0.95);
}

#search-modal.active {
  opacity: 1;
  visibility: visible;
}

.search-content-area {
  width: 90%;
  max-width: 700px;
  position: relative;
  transform: translateY(-50px);
  transition: transform 0.3s ease;
}

#search-modal.active .search-content-area {
  transform: translateY(0);
}

.search-close {
  position: absolute;
  top: -50px;
  right: 0;
  color: var(--text-primary);
  font-size: 28px;
  cursor: pointer;
  transition: transform 0.3s ease, color 0.3s ease;
}

[data-bs-theme="dark"] .search-close {
  color: var(--text-inverse);
}

.search-close:hover {
  transform: rotate(90deg);
  color: var(--text-secondary);
}

[data-bs-theme="dark"] .search-close:hover {
  color: var(--text-tertiary);
}

.search-form {
  position: relative;
}

.search-input {
  width: 100%;
  padding: 20px 60px 20px 30px;
  background-color: var(--bg-primary);
  border: 2px solid var(--border-color);
  border-radius: 50px;
  color: var(--text-primary);
  font-size: 20px;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-md);
}

[data-bs-theme="dark"] .search-input {
  background-color: var(--bg-secondary);
  border-color: var(--border-dark);
  color: var(--text-inverse);
  box-shadow: var(--shadow-lg);
}

.search-input:focus {
  border-color: var(--border-focus);
  outline: none;
  box-shadow: var(--shadow-lg);
}

[data-bs-theme="dark"] .search-input:focus {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-xl);
}

.search-input::placeholder {
  color: var(--text-muted);
}

[data-bs-theme="dark"] .search-input::placeholder {
  color: var(--text-tertiary);
}

.search-submit {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 24px;
  cursor: pointer;
  transition: color 0.3s ease;
}

[data-bs-theme="dark"] .search-submit {
  color: var(--text-tertiary);
}

.search-submit:hover {
  color: var(--text-primary);
}

[data-bs-theme="dark"] .search-submit:hover {
  color: var(--text-inverse);
}

.search-hint {
  text-align: center;
  color: var(--text-secondary);
  margin-top: 20px;
  font-size: 16px;
}

[data-bs-theme="dark"] .search-hint {
  color: var(--text-tertiary);
}
/* Header Design */
/* Header Design */
.header {
  background-color: var(--header-light) !important;
}

.header .logo-img {
  max-width: 400px;
  max-height: 30px;
}

.header .navbar-brand {
  color: var(--text-color) !important;
  font-size: 1.5rem;
  font-weight: bold;
}

.header .navbar-nav {
  display: flex;
  align-items: center;
}

.header .nav-link {
  color: var(--text-color) !important;
  padding: 10px;
  font-size: 1rem;
  font-weight: 600;
}

.header .nav-link:hover {
  color: var(--primary) !important;
  text-decoration: none;
  background: none;
}

header.header .toolbar .nav-link {
  padding: 0.5rem 1rem;
  border-radius: var(--custom-radius) !important;
  color: var(--gray) !important;
  position: relative;
  font-size: 1em;
}

.header .cart-btn {
  display: flex;
  align-items: center;
}

.header .cart-btn i {
  margin-right: 5px;
}

/* Sidebar Design */
.widdxsidebar {
  width: 250px;
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  background: var(--btn-bg) !important;
  box-shadow: 2px 0 5px rgba(var(--dark-rgb), 0.3);
  z-index: 1000;
  overflow-y: auto;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

.widdxsidebar.show {
  transform: translateX(0);
}

/* Sidebar Header */
.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: var(--btn-bg) !important;
  color: var(--heading-color) !important;
  border-bottom: 1px solid var(--border-color) !important;
}

.sidebar-header h5 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: bold;
}

.sidebar-header .close {
  font-size: 1.5rem;
  line-height: 1;
  color: var(--heading-color) !important;
  background: none;
  border: none;
  cursor: pointer;
  transition: color 0.3s;
}

.sidebar-header .close:hover {
  color: var(--primary) !important;
}

/* Sidebar Content */
.sidebar-content {
  padding: 15px;
}

/* Search Box Design */
.search-wrapper {
  margin-bottom: 15px;
}

.search-wrapper .input-group.search {
  width: 100%;
}

.search-wrapper .input-group .form-control {
  border-radius: var(--custom-radius) !important;
  border: 1px solid var(--border-color) !important;
  padding: 10px;
}

.search-wrapper .input-group .btn-default {
  border-radius: var(--custom-radius) !important;
  border: 1px solid var(--border-color) !important;
  background-color: var(--btn-bg) !important;
  color: var(--body-color) !important;
}

.search-wrapper .input-group .btn-default:hover {
  background-color: var(--border-color) !important;
}
/* Navigation List Enhancement */
.widdxsidebar .navbar-nav {
  padding-left: 0;
  margin: 0;
}

.widdxsidebar .nav-item {
  list-style-type: none;
}

.widdxsidebar .nav-link {
  display: block;
  padding: 10px 15px;
  color: var(--body-color) !important;
  text-decoration: none;
}

.widdxsidebar .nav-link:hover {
  background-color: var(--gray-light) !important;
  color: var(--primary) !important;
}

.widdxsidebar .nav-item + .nav-item {
  border-top: 1px solid var(--border-color) !important;
}

.widdxsidebar .sidebar-footer {
  position: absolute;
  bottom: 0;
  width: 100%;
}

.widdxsidebar .btn {
  width: 100%;
  text-align: left;
}

.widdxsidebar .social {
  margin-top: 10px;
}

/* Add responsive design if needed */
@media (max-width: 767px) {
  .widdxsidebar {
    width: 100%;
    transform: translateX(-100%);
  }

  .widdxsidebar.collapse.show {
    transform: translateX(0);
  }
}

/* Navigation Items */
.nav-item {
  position: relative;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  color: var(--body-color) !important;
  text-decoration: none;
  transition: background-color 0.3s, color 0.3s;
}

.nav-link:hover,
.nav-link:focus {
  background-color: var(--gray-light) !important;
  color: var(--primary) !important;
}

/* Dropdown Menu */
.dropdown-menu {
  border: 1px solid var(--border-color) !important;
  border-radius: var(--custom-radius) !important;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.dropdown-item {
  padding: 0.5rem 1rem;
  color: var(--body-color) !important;
  text-decoration: none;
}

.dropdown-item:hover,
.dropdown-item:focus {
  background-color: var(--gray-light) !important;
  color: var(--primary) !important;
}

.dropdown-divider {
  height: 0;
  margin: 0.5rem 0;
  overflow: hidden;
  border-top: 1px solid var(--border-color) !important;
}

/* Badge */
.badge-info {
  background-color: var(--info) !important;
  color: var(--btn-bg) !important;
  border-radius: 0.2rem;
  padding: 0.2rem 0.6rem;
}

/* More Dropdown */
.more-dropdown {
  display: none;
}

.more-dropdown.show {
  display: block;
}

/* For Responsive Design */
@media (max-width: 767.98px) {
  .navbar-expand-xl .nav-link {
    padding: 10px;
  }
}

/* CSS إضافي لتخصيص ألوان الأزرار */
.list-group-item {
  border: 1px solid var(--border-color) !important;
  border-radius: var(--custom-radius) !important;
}

.list-group-item-action.active {
  background-color: var(--gray-light) !important;
  color: var(--body-color) !important;
}

.modal-footer {
  padding: 1rem;
}

.btn-primary {
  background-color: var(--primary) !important;
  border-color: var(--primary) !important;
}

.btn-primary:hover {
  background-color: var(--primary-dark) !important;
  border-color: var(--primary-dark) !important;
}

/* تأكد من أن جميع الكروت لها نفس الارتفاع */
.owl-carousel .item .card {
  min-height: 400px;
  /* يمكنك تعديل هذا الارتفاع حسب الحاجة */
}

/* تأكد من توزيع النصوص بشكل متساوٍ */
.widdxcard-body {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  height: 512px;
  padding: 6%;
}

/* تأثير fadeIn */
.animated.fadeIn {
  opacity: 0;
  animation: fadeIn 1s forwards;
}

.owl-theme .owl-nav {
  margin-top: 10px;
  display: none;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* تأثير fadeOut */
.animated.fadeOut {
  opacity: 1;
  animation: fadeOut 1s forwards;
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

/* Hosting item and feature box styles moved to hosting-features.css */

/* Social Icons Styles */
.social-icons {
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
  justify-content: center;
}

.social-icons .list-inline-item {
  margin: 0 5px; /* Adjust spacing between icons */
}

.social-icons .btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px; /* Size of the icon button */
  height: 40px; /* Size of the icon button */
  font-size: 20px; /* Size of the icon */
  color: var(--body-color); /* Default color for icons */
  transition: background-color 0.3s, color 0.3s; /* Smooth transition */
}

.social-icons .btn-icon:hover {
  background-color: var(--primary); /* Background color on hover */
  color: var(--btn-bg); /* Icon color on hover */
}

/* Ensure that the icon size is responsive */
.social-icons .btn-icon i {
  line-height: 1;
}

.all-in-one-section h1 {
  color: var(--heading-color);
  font-size: 2rem;
}

.all-in-one-section ul li {
  color: var(--body-color);
  font-size: 1.125rem;
}

.all-in-one-section ul li i {
  font-size: 1.25rem;
}

.all-in-one-section img {
  max-width: 100%;
  height: auto;
  width: 100%;
  max-height: 500px;
  object-fit: cover;
}

.maximize-website-speed-section h1 {
  color: var(--heading-color);
  font-size: 2rem;
}

.maximize-website-speed-section ul li {
  color: var(--body-color);
  font-size: 1.125rem;
}

.maximize-website-speed-section ul li i {
  font-size: 1.25rem;
}

.maximize-website-speed-section img {
  max-width: 90%;
  height: auto;
  width: 100%;
  max-height: 500px;
  object-fit: cover;
}

.background-video-overly {
  background-color: rgba(var(--primary-color), 0.37);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.promo-banner {
  margin: 0;
  padding: 15px 10px 20px 10px;
  background-color: #fff;
  font-size: 1em;
  overflow: auto;
}
.promo-banner .content {
  margin-left: 185px;
}
@media (max-width: 767.98px) {
  .promo-banner .content {
    margin: 10px 0 0 0;
  }
}
.promo-banner .icon-left {
  text-align: center;
  width: 155px;
  float: left;
}
@media (max-width: 767.98px) {
  .promo-banner .icon-left {
    width: 90px;
    float: none;
  }
}
.promo-banner .icon-left img {
  max-width: 100%;
  max-height: 100%;
}
@media (max-width: 767.98px) {
  .promo-banner form {
    text-align: center;
  }
}
.promo-banner .icon {
  float: left;
  margin: 0 20px 0 0;
}
.promo-banner h3 {
  margin: 0;
  padding: 0;
  font-size: 24px;
  font-weight: 300;
}
.promo-banner h4 {
  font-size: 18px;
  font-weight: 300;
}
.promo-banner p {
  margin: 5px 0;
}
.promo-banner ul {
  margin: 0;
  padding: 0;
  list-style: none;
  font-size: 0.92em;
}
.promo-banner ul li {
  margin-bottom: 4px;
}
@media (min-width: 1366px) {
  .promo-banner ul li {
    width: 50%;
  }
  .promo-banner ul li.left {
    float: left;
  }
  .promo-banner ul li.right {
    float: right;
  }
}
.promo-banner .fa:not(.fa-spinner) {
  font-size: 1.2em;
  color: var(--success-color);
  margin-left: 20px;
  margin-right: 10px;
}
.promo-banner a {
  color: var(--secondary-color);
  text-decoration: underline;
}
.promo-banner .btn {
  margin: 6px 0 0 0;
  padding: 8px 30px;
  background-color: var(--secondary-color);
  border: 0;
  color: var(--white);
}
@media (max-width: 650px) {
  .promo-banner .icon {
    width: 100%;
    margin-bottom: 15px;
  }
}
.promo-banner-rounded {
  border-radius: 4px;
}
.promo-banner-slim {
  padding: 10px;
}
.promo-banner-slim .icon-left {
  width: 85px;
}
.promo-banner-slim .content {
  margin-left: 110px;
}
.promo-banner-slim h3 {
  font-size: 18px;
}
.promo-banner-slim h4 {
  font-size: 14px;
}
.promo-banner-slim .btn {
  margin: 0 20px 0 0;
  padding: 6px 20px;
}
.promo-banner.symantec {
  border-top: 3px solid var(--success-color);
}
.promo-banner.symantec .btn {
  background-color: var(--success-color);
}
.promo-banner.weebly {
  border-top: 3px solid var(--primary-color);
}
.promo-banner.weebly .btn {
  background-color: var(--primary-color);
}
.promo-banner.spamexperts {
  border-top: 3px solid var(--primary-light);
}
.promo-banner.spamexperts .btn {
  background-color: var(--primary-light);
}
.promo-banner-slim.weebly .icon-left {
  width: 160px;
}
.promo-banner-slim.weebly .content {
  margin-left: 185px;
}
.promo-banner.sitelock {
  border-top: 3px solid var(--warning-color);
}
.promo-banner.sitelock .btn {
  background-color: var(--warning-color);
}
.promo-banner.codeguard {
  border-top: 3px solid var(--success-light);
}
.promo-banner.codeguard a {
  color: var(--success-light);
}
.promo-banner.codeguard .btn {
  background-color: var(--success-light);
}
.promo-banner.sitelockvpn {
  border-top: 3px solid var(--gray-600);
}
.promo-banner.sitelockvpn .btn {
  background-color: var(--gray-600);
}
.promo-banner.nordvpn {
  border-top: 3px solid var(--primary-color);
}
.promo-banner.nordvpn .btn {
  background-color: var(--primary-color);
}
.promo-banner.marketgoo {
  border-top: 3px solid var(--primary-light);
}
.promo-banner.marketgoo .btn {
  background-color: var(--primary-light);
}
.promo-banner.ox {
  border-top: 3px solid var(--gray-500);
}
.promo-banner.ox a {
  color: var(--gray-500);
}
.promo-banner.ox .btn {
  background-color: var(--gray-500);
}
.promo-banner.sitebuilder {
  border-top: 3px solid var(--secondary-color);
}
.promo-banner.sitebuilder a {
  color: var(--secondary-color);
}
.promo-banner.sitebuilder .btn {
  background-color: var(--secondary-color);
}
.promo-banner.xovinow {
  border-top: 3px solid var(--primary-dark);
}
.promo-banner.xovinow a {
  color: var(--primary-dark);
}
.promo-banner.xovinow .btn {
  background-color: var(--primary-dark);
}
.promo-banner.threesixtymonitoring {
  border-top: 3px solid var(--error-color);
}
.promo-banner.threesixtymonitoring .btn {
  background-color: var(--error-color);
}
.promo-banner.threesixtymonitoring a {
  color: var(--error-color);
}

.popup {
  display: none; /* إخفاء الكرت بشكل افتراضي */
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 20px;
  border-radius: 10px;
  z-index: 1000; /* التأكد من ظهور الكرت فوق جميع العناصر الأخرى */
}

.popup-content {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  padding: 20px;
  border-radius: 10px;
  text-align: center;
}

[data-bs-theme="dark"] .popup-content {
  background-color: var(--bg-secondary);
  color: var(--text-inverse);
}

.popup h2 {
  margin-top: 0;
  font-size: 24px;
}

.popup p {
  font-size: 16px;
  margin: 10px 0;
}

.popup-images {
  margin: 20px 0;
}

.popup-images img {
  width: 100%; /* اجعل الصورة تتناسب مع حجم العنصر الحاوي */
  max-width: 300px; /* تحديد أقصى عرض للصورة */
  border-radius: 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  margin: 5px;
}

.popup button {
  background-color: var(--success-color); /* لون الخلفية للزر */
  color: var(--white); /* لون النص في الزر */
  border: none;
  padding: 10px 20px;
  margin: 10px 5px;
  cursor: pointer;
  border-radius: 5px;
  font-size: 16px;
}

.popup button:hover {
  background-color: var(--success-dark); /* تغيير اللون عند التمرير فوق الزر */
}

#close-popup {
  background-color: var(--error-color); /* لون الخلفية لزر الإغلاق */
}

#close-popup:hover {
  background-color: var(--error-dark); /* تغيير اللون عند التمرير فوق زر الإغلاق */
}

.popup {
  display: none; /* إخفاء الكرت بشكل افتراضي */
  position: fixed;
  bottom: 20px; /* المسافة من أسفل الشاشة */
  right: 20px; /* المسافة من اليمين */
  padding: 15px;
  border-radius: 10px;
  z-index: 1000; /* التأكد من ظهور الكرت فوق جميع العناصر الأخرى */
  width: 90%; /* عرض الكرت بنسبة مئوية */
  max-width: 400px; /* تحديد أقصى عرض للكرت */
  height: auto; /* ارتفاع الكرت تلقائي */
  display: flex;
  flex-direction: column; /* ترتيب العناصر عمودياً */
}

.popup-content {
  display: flex;
  flex-direction: column; /* ترتيب العناصر عمودياً في المحتوى */
  width: 100%;
  height: 100%;
}

.popup-left,
.popup-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.popup-left {
  overflow: hidden;
  position: relative;
}

.popup-slider {
  position: relative;
  overflow: hidden;
}

.popup-image {
  width: 100%;
  height: auto;
  cursor: pointer;
  border-radius: 5px;
  margin-bottom: 5px;
}

.lightbox {
  display: none; /* إخفاء الضوء الكبير بشكل افتراضي */
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  justify-content: center;
  align-items: center;
  z-index: 2000; /* أعلى من الكرت المنبثق */
}

.lightbox img {
  max-width: 90%;
  max-height: 80%;
  border-radius: 5px;
}

.close-lightbox {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 24px;
  color: white;
  cursor: pointer;
  z-index: 2001;
}

.popup-right {
  padding: 10px;
  text-align: center;
}

.popup-right h2 {
  margin-top: 0;
  font-size: 18px; /* حجم خط مناسب للشاشات الصغيرة */
}

.popup-right p {
  font-size: 14px;
  margin: 10px 0;
}

.popup-right button {
  background-color: var(--success-color); /* لون الخلفية للزر */
  color: var(--white); /* لون النص في الزر */
  border: none;
  padding: 10px 20px;
  margin: 5px 0;
  cursor: pointer;
  border-radius: 5px;
  font-size: 14px;
}

.popup-right button:hover {
  background-color: var(--success-dark); /* تغيير اللون عند التمرير فوق الزر */
}

#close-popup {
  background-color: var(--error-color); /* لون الخلفية لزر الإغلاق */
}

#close-popup:hover {
  background-color: var(--error-dark); /* تغيير اللون عند التمرير فوق زر الإغلاق */
}

/* Media Queries for Responsive Design */
@media (min-width: 768px) {
  .popup {
    flex-direction: row; /* ترتيب العناصر في صف عند الشاشات الكبيرة */
    width: 70%; /* عرض أكبر على الشاشات الكبيرة */
    max-width: 600px; /* أقصى عرض أكبر على الشاشات الكبيرة */
  }

  .popup-left,
  .popup-right {
    flex: 1;
    padding: 10px;
  }

  .popup-right {
    padding-left: 20px; /* مساحة بين النص والصور */
  }
}

@media (max-width: 767px) {
  .popup-right {
    padding: 10px;
  }
}


.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden; /* لتجنب تجاوز المحتوى للفيديو */
}

.background-video-overly {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5); /* إضافة طبقة تعتيم لتحسين وضوح النص */
  z-index: 0; /* خلف الفيديو */
}

#myVideo {
  object-fit: cover; /* يجعل الفيديو يملأ العنصر دون تشويش */
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: -1; /* خلف النصوص */
}




#seoResults .card {
  border: none;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

#seoResults .card-title {
  font-weight: 600;
  font-size: 1.25rem;
  color: var(--primary-color);
}

#seoResults .list-group-item {
  background-color: var(--bg-secondary);
  border: 0;
  padding: 10px 20px;
}

#seoResults .card-body p {
  margin-bottom: 0;
  font-size: 1.1em;
  color: var(--text-primary);
}

.input-group .form-control {
  border-radius: 0.25rem;
}

.input-group .input-group-append .btn {
  border-radius: 0.25rem;
}

#fullpage-overlay {
  transition: opacity 10s ease; /* تغيير الوقت حسب الحاجة */
}

/* Theme Toggle Button Styles - Additional */
body[data-bs-theme="dark"] {
  background-color: var(--dark);
  color: var(--text-color);
}

body[data-bs-theme="light"] {
  background-color: var(--bg-primary);
  color: var(--text-color);
}

.card, .panel, .container-fluid {
  background-color: var(--header-light);
  color: var(--text-color);
}

.header {
  background-color: var(--header-light) !important;
}

.footer {
  background-color: var(--header-light) !important;
  color: var(--text-color) !important;
}

