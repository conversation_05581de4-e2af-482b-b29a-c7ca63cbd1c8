/*
 * Modern Hero Slider Styles
 * For WIDDX WHMCS Theme
 * With Light/Dark Mode Support
 */

/* Import company brand colors and variables */
@import url('variables.css');

/* Hero Slider Variables */
:root {
    --hero-bg-light: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-200) 100%);
    --hero-bg-dark: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-800) 100%);
    --hero-title-color-light: var(--heading-color);
    --hero-title-color-dark: var(--gray-50);
    --hero-text-color-light: var(--text-color);
    --hero-text-color-dark: var(--gray-400);
    --hero-feature-title-light: var(--heading-color);
    --hero-feature-title-dark: var(--gray-300);
    --hero-feature-text-light: var(--text-color);
    --hero-feature-text-dark: var(--gray-400);
    --hero-icon-bg-light: var(--primary-light);
    --hero-icon-bg-dark: rgba(var(--primary-color), 0.2);
    --hero-icon-color-light: var(--primary-color);
    --hero-icon-color-dark: var(--primary-color);
    --hero-dot-bg-light: rgba(var(--black), 0.2);
    --hero-dot-bg-dark: rgba(var(--white), 0.2);
    --hero-dot-active-light: var(--primary-color);
    --hero-dot-active-dark: var(--primary-color);
    --hero-transition: all 0.3s ease;
}

/* Main Container */
.modern-hero-slider {
    position: relative;
    overflow: hidden;
    padding: 80px 0;
    background: var(--hero-bg-light);
    transition: var(--hero-transition);
}

/* Animated Background Elements */
.hero-bg-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    overflow: hidden;
}

.hero-shape {
    position: absolute;
    border-radius: 50%;
    opacity: 0.5;
}

.hero-shape-1 {
    top: -50px;
    right: -50px;
    width: 300px;
    height: 300px;
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    animation: float 15s ease-in-out infinite;
}

.hero-shape-2 {
    bottom: -100px;
    left: -100px;
    width: 400px;
    height: 400px;
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary) 100%);
    animation: float 18s ease-in-out infinite reverse;
}

.hero-shape-3 {
    top: 40%;
    left: 15%;
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-darker) 100%);
    animation: float 12s ease-in-out infinite;
}

.hero-shape-4 {
    bottom: 30%;
    right: 10%;
    width: 150px;
    height: 150px;
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary) 100%);
    animation: float 10s ease-in-out infinite reverse;
}

@keyframes float {
    0% { transform: translateY(0) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(5deg); }
    100% { transform: translateY(0) rotate(0deg); }
}

/* Slider Content */
.owl-carousel {
    position: relative;
    z-index: 2;
}

.hero-slide {
    padding: 20px 0;
}

/* Content Column */
.hero-content-col {
    padding: 30px 15px;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-badge {
    display: inline-block;
    padding: 8px 16px;
    background-color: var(--primary);
    color: white;
    font-size: 14px;
    font-weight: 600;
    border-radius: 30px;
    margin-bottom: 20px;
    letter-spacing: 1px;
    text-transform: uppercase;
    transition: var(--hero-transition);
}

[data-bs-theme="dark"] .hero-badge {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
}

.hero-title {
    font-size: 2.8rem;
    font-weight: 700;
    margin-bottom: 30px;
    color: var(--hero-title-color-light);
    line-height: 1.2;
    transition: var(--hero-transition);
}

/* Feature Items */
.hero-features {
    margin-bottom: 30px;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
}

.feature-icon {
    width: 40px;
    height: 40px;
    background-color: var(--hero-icon-bg-light);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
    transition: var(--hero-transition);
}

.feature-icon i {
    color: var(--hero-icon-color-light);
    font-size: 18px;
    transition: var(--hero-transition);
}

.feature-text {
    flex: 1;
}

.feature-text strong {
    display: block;
    font-weight: 600;
    margin-bottom: 3px;
    color: var(--hero-feature-title-light);
    transition: var(--hero-transition);
}

.feature-text span {
    color: var(--hero-feature-text-light);
    font-size: 0.95rem;
    line-height: 1.5;
    transition: var(--hero-transition);
}

/* CTA Section */
.hero-cta {
    margin-top: 30px;
}

.promo-text {
    font-size: 1.1rem;
    margin-bottom: 10px;
    color: var(--hero-feature-text-light);
    transition: var(--hero-transition);
}

.price-tag {
    display: flex;
    align-items: baseline;
    margin-bottom: 10px;
}

.currency {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary);
    transition: var(--hero-transition);
}

.amount {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary);
    line-height: 1;
    margin: 0 5px;
    transition: var(--hero-transition);
}

.period {
    font-size: 1.1rem;
    color: var(--text-muted);
    transition: var(--hero-transition);
}

[data-bs-theme="dark"] .currency,
[data-bs-theme="dark"] .amount {
    text-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.regular-price {
    font-size: 0.9rem;
    color: var(--text-muted);
    margin-bottom: 20px;
    transition: var(--hero-transition);
}

.cta-button {
    display: inline-block;
    padding: 12px 30px;
    background-color: var(--primary);
    color: white;
    font-weight: 600;
    border-radius: 30px;
    text-decoration: none;
    transition: var(--hero-transition);
    border: 2px solid var(--primary);
}

.cta-button:hover {
    background-color: transparent;
    color: var(--primary);
    transform: translateY(-3px);
    text-decoration: none;
}

[data-bs-theme="dark"] .cta-button:hover {
    color: var(--primary-light);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Image Column */
.hero-image-col {
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-image {
    position: relative;
    z-index: 2;
    max-width: 100%;
    animation: image-float 6s ease-in-out infinite;
    transition: var(--hero-transition);
}

[data-bs-theme="dark"] .hero-image {
    filter: drop-shadow(0 10px 15px rgba(0, 0, 0, 0.3));
}

@keyframes image-float {
    0% { transform: translateY(0); }
    50% { transform: translateY(-15px); }
    100% { transform: translateY(0); }
}

/* Slider Navigation */
.slider-nav {
    position: relative;
    z-index: 3;
    margin-top: 30px;
}

.slider-dots {
    display: flex;
    justify-content: center;
}

/* Owl Carousel Custom Styles */
.owl-dots {
    display: flex;
    justify-content: center;
    margin-top: 30px;
}

.owl-dot {
    width: 12px;
    height: 12px;
    background-color: var(--hero-dot-bg-light) !important;
    border-radius: 50%;
    margin: 0 5px;
    transition: var(--hero-transition);
}

.owl-dot.active {
    background-color: var(--hero-dot-active-light) !important;
    transform: scale(1.2);
}

/* Dark Mode Support */
[data-bs-theme="dark"] .modern-hero-slider {
    background: var(--hero-bg-dark);
}

[data-bs-theme="dark"] .hero-title {
    color: var(--hero-title-color-dark);
}

[data-bs-theme="dark"] .feature-icon {
    background-color: var(--hero-icon-bg-dark);
}

[data-bs-theme="dark"] .feature-icon i {
    color: var(--hero-icon-color-dark);
}

[data-bs-theme="dark"] .feature-text strong {
    color: var(--hero-feature-title-dark);
}

[data-bs-theme="dark"] .feature-text span {
    color: var(--hero-feature-text-dark);
}

[data-bs-theme="dark"] .promo-text {
    color: var(--hero-feature-text-dark);
}

[data-bs-theme="dark"] .period,
[data-bs-theme="dark"] .regular-price {
    color: var(--text-muted);
}

[data-bs-theme="dark"] .owl-dot {
    background-color: var(--hero-dot-bg-dark) !important;
}

[data-bs-theme="dark"] .owl-dot.active {
    background-color: var(--hero-dot-active-dark) !important;
}

/* Responsive Styles */
@media (max-width: 991.98px) {
    .modern-hero-slider {
        padding: 60px 0;
    }

    .hero-title {
        font-size: 2.2rem;
    }

    .hero-image-col {
        margin-top: 30px;
    }
}

@media (max-width: 767.98px) {
    .modern-hero-slider {
        padding: 40px 0;
    }

    .hero-title {
        font-size: 1.8rem;
    }

    .hero-badge {
        font-size: 12px;
        padding: 6px 12px;
    }

    .amount {
        font-size: 2.5rem;
    }
}
