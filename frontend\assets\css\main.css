/*
 * WHMCS WIDDX Theme
 * Main CSS file
 */

/* Import company brand colors and variables */
@import url('variables.css');

/* Base Utilities */
.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.d-flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.justify-content-center {
  justify-content: center;
}

.align-items-center {
  align-items: center;
}

.w-100 {
  width: 100%;
}

.h-100 {
  height: 100%;
}

.m-0 {
  margin: 0;
}

.p-0 {
  padding: 0;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .d-md-none {
    display: none !important;
  }

  .d-md-block {
    display: block !important;
  }

  .text-md-center {
    text-align: center !important;
  }
}


/* ===== HERO SECTION ===== */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background: transparent;
    transition: all 0.3s ease;
    padding: 120px 0 80px;
}

/* ===== DARK THEME BACKGROUND ADJUSTMENTS ===== */
[data-theme="dark"] .modern-gradient-background {
    background: var(--background-primary);
}

[data-theme="dark"] .gradient-layer-1 {
    background: radial-gradient(circle at 20% 30%, rgba(var(--gray-900), 0.9) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(var(--primary-color), 0.15) 0%, transparent 60%),
                radial-gradient(circle at 40% 80%, rgba(var(--primary-light), 0.1) 0%, transparent 50%);
}

[data-theme="dark"] .gradient-layer-2 {
    background: radial-gradient(circle at 60% 20%, rgba(var(--gray-700), 0.3) 0%, transparent 40%),
                radial-gradient(circle at 10% 60%, rgba(var(--primary-color), 0.08) 0%, transparent 70%),
                radial-gradient(circle at 90% 40%, rgba(var(--primary-light), 0.12) 0%, transparent 55%);
}
